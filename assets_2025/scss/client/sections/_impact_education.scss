// Impact Education Section - Progress Bar Animations
// Simple, reliable approach

// Target progress bars with specific aria values and add initial state + transition
.progress-bar.education-progress-bar {
    width: 0% !important;
    transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

    .d-flex {
        opacity: 0;
        transition: opacity 0.3s ease-in-out 1.2s;
    }
}

// When section gets animate-progress class, animate the bars
section.animate-progress {

    // First progress bar (81%)
    .progress-bar.education-progress-bar[aria-valuenow="81"] {
        width: 81% !important;

        .d-flex {
            opacity: 1;
        }
    }

    // Second progress bar (58%) with stagger
    .progress-bar.education-progress-bar[aria-valuenow="58"] {
        width: 58% !important;
        transition-delay: 0.4s;

        .d-flex {
            opacity: 1;
            transition-delay: 1.6s;
        }
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .progress-bar.education-progress-bar {
        transition-duration: 0.3s;

        .d-flex {
            transition-delay: 0.1s;
        }
    }

    section.animate-progress .progress-bar.education-progress-bar[aria-valuenow="58"] {
        transition-delay: 0.1s;

        .d-flex {
            transition-delay: 0.2s;
        }
    }
}