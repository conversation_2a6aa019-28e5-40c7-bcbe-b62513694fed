// Impact Education Section - Progress Bar Animations
// Scoped to only affect progress bars within sections that have graduation cap + progress bars

// Use a more specific and reliable selector approach
// Target sections that will get the animate-progress class
section.animate-progress {

    // Target all progress bars in this section initially
    .progress-bar {
        transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

        // Text content
        .d-flex {
            opacity: 0;
            transition: opacity 0.3s ease-in-out 1.2s;
        }
    }

    // First progress bar (the one with 81%)
    .progress-bar[aria-valuenow="81"] {
        width: 81% !important;

        .d-flex {
            opacity: 1;
        }
    }

    // Second progress bar (the one with 58%) with stagger
    .progress-bar[aria-valuenow="58"] {
        width: 58% !important;
        transition-delay: 0.4s;

        .d-flex {
            opacity: 1;
            transition-delay: 1.6s;
        }
    }

    // Fallback for reduced motion preferences
    @media (prefers-reduced-motion: reduce) {
        .progress-bar {
            transition-duration: 0.3s;

            .d-flex {
                transition-delay: 0.1s;
            }
        }

        .progress-bar[aria-valuenow="58"] {
            transition-delay: 0.1s;

            .d-flex {
                transition-delay: 0.2s;
            }
        }
    }
}

// Initial state - hide progress bars before animation
// Target any section that contains graduation cap and progress bars
section:has(.fa-graduation-cap):has(.progress-bar) .progress-bar,
section .fa-graduation-cap ~ * .progress-bar,
.section .progress-bar[aria-valuenow="81"],
.section .progress-bar[aria-valuenow="58"] {
    width: 0% !important;

    .d-flex {
        opacity: 0;
    }
}

// Override the initial state when animation class is applied
section.animate-progress .progress-bar[aria-valuenow="81"],
section.animate-progress .progress-bar[aria-valuenow="58"] {
    // This will be handled by the rules above
}