// Impact Education Section - Progress Bar Animations
// Scoped to only affect progress bars within sections that have graduation cap + progress bars

// Target any section that contains both graduation cap and progress bars
section:has(.fa-graduation-cap):has(.progress-bar) {

    // Initially hide progress bars (set to 0% width)
    .progress-bar {
        width: 0% !important;
        transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

        // Ensure text is visible even when bar is at 0%
        .d-flex {
            opacity: 0;
            transition: opacity 0.3s ease-in-out 1.2s; // Delay text appearance until bar starts filling
        }
    }

    // Animation class applied when section comes into view
    &.animate-progress {
        .progress-bar {
            // First progress bar (81%)
            &:first-of-type {
                width: 81% !important;

                .d-flex {
                    opacity: 1;
                }
            }

            // Second progress bar (58%) with slight delay
            &:last-of-type {
                width: 58% !important;
                transition-delay: 0.3s; // Stagger the animation

                .d-flex {
                    opacity: 1;
                    transition-delay: 1.5s; // Slightly later text appearance for second bar
                }
            }
        }
    }

    // Fallback for reduced motion preferences
    @media (prefers-reduced-motion: reduce) {
        .progress-bar {
            transition-duration: 0.3s;

            .d-flex {
                transition-delay: 0.1s;
            }
        }

        &.animate-progress .progress-bar:last-of-type {
            transition-delay: 0.1s;

            .d-flex {
                transition-delay: 0.2s;
            }
        }
    }
}

// Fallback for browsers that don't support :has() selector
// Target sections with specific class patterns that might be used
.section.impact_education,
.section[class*="impact_education"],
.section[class*="impact-education"],
.impact_education,
.impact-education {

    // Only apply if this section contains progress bars
    &:has(.progress-bar),
    .progress-bar {
        // Initially hide progress bars (set to 0% width)
        width: 0% !important;
        transition: width 1.8s cubic-bezier(0.4, 0, 0.2, 1);

        // Ensure text is visible even when bar is at 0%
        .d-flex {
            opacity: 0;
            transition: opacity 0.3s ease-in-out 1.2s;
        }
    }

    // Animation class applied when section comes into view
    &.animate-progress {
        .progress-bar {
            // First progress bar (81%)
            &:first-of-type {
                width: 81% !important;

                .d-flex {
                    opacity: 1;
                }
            }

            // Second progress bar (58%) with slight delay
            &:last-of-type {
                width: 58% !important;
                transition-delay: 0.3s;

                .d-flex {
                    opacity: 1;
                    transition-delay: 1.5s;
                }
            }
        }
    }
}