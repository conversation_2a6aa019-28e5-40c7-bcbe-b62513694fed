// LOADING UP ALL THE FILES...
// This is all of the base bootstrap, set up with custom global
// variables for the client styles.

// BOOTSTRAP
// First load up <PERSON>'s banner mixin
@import "../bootstrap/mixins/banner";
@include bsBanner("");

// BOOTSTRAP
// Load up BS's functions before custom, or some of our variables get grumpy.
@import "../bootstrap/functions";

// CUSTOM
// Fonts
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

// CUSTOM
// Must load up our custom overriding variables before BS's.
// @import "fonts";         // CUSTOM fonts
@import "variables";

// REMOVE FROM MAPS
$colors: map-remove($colors, "indigo", "pink", "yellow", "teal");
$theme-colors: map-remove($theme-colors, "indigo", "pink", "yellow", "teal");

// BOOTSTRAP
// Configuration
@import "../bootstrap/variables";
@import "../bootstrap/variables-dark";
@import "../bootstrap/maps";
@import "../bootstrap/mixins";
@import "../bootstrap/utilities";

// CUSTOM OVERRIDES
// Configuration
// @import "variables-dark";
// @import "maps";
@import "mixins";
@import "utilities";

// BOOTSTRAP
// Layout & components
@import "../bootstrap/root";
@import "../bootstrap/reboot";
@import "../bootstrap/type";
@import "../bootstrap/images";
@import "../bootstrap/containers";
@import "../bootstrap/grid";
@import "../bootstrap/tables";
@import "../bootstrap/forms";
@import "../bootstrap/buttons";
@import "../bootstrap/transitions";
@import "../bootstrap/dropdown";
@import "../bootstrap/button-group";
@import "../bootstrap/nav";
@import "../bootstrap/navbar";
@import "../bootstrap/card";
@import "../bootstrap/accordion";
@import "../bootstrap/breadcrumb";
@import "../bootstrap/pagination";
@import "../bootstrap/badge";
@import "../bootstrap/alert";
@import "../bootstrap/progress";
@import "../bootstrap/list-group";
@import "../bootstrap/close";
@import "../bootstrap/toasts";
@import "../bootstrap/modal";
@import "../bootstrap/tooltip";
@import "../bootstrap/popover";
@import "../bootstrap/carousel";
@import "../bootstrap/spinners";
@import "../bootstrap/offcanvas";
@import "../bootstrap/placeholders";

// BOOTSTRAP
// Helpers
@import "../bootstrap/helpers";

// BOOTSTRAP
// Utilities
@import "../bootstrap/utilities/api";
// scss-docs-end import-stack

// CUSTOM OVERRIDES
// Layout & components
@import "root";
@import "reboot";
@import "type";
//@import "images";
@import "containers";
//@import "grid";
//@import "tables";
@import "forms";
@import "buttons";
//@import "transitions";
//@import "dropdown";
//@import "button-group";
//@import "nav";
//@import "navbar";
//@import "card";
//@import "accordion";
//@import "breadcrumb";
//@import "pagination";
//@import "badge";
//@import "alert";
//@import "progress";
//@import "list-group";
//@import "close";
//@import "toasts";
//@import "modal";
//@import "tooltip";
//@import "popover";
//@import "carousel";
//@import "spinners";
//@import "offcanvas";
//@import "placeholders";

// BOOTSTRAP
// Helpers
@import "helpers";



// CUSTOM LAYOUT
@import "site_layout/site_header";
@import "site_layout/site_main";
@import "site_layout/site_footer";
@import "sections/sections";
@import "sections/hero";
@import "sections/collapsing";
@import "sections/staff";
@import "sections/events";
@import "sections/sponsors";
@import "sections/impact";
@import "sections/news";
@import "sections/impact_education";
@import "sections/impact_skills";




