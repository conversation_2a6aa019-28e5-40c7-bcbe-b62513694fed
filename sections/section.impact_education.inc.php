<?php
/**
 * File: section.impact_education.inc.php
 * 
 * @see "README" in this folder for instructions on usage. ;)
 *    
 */
if(!class_exists('impact_education')) {

    /**
     * PageSection class.
     */
    class impact_education
        extends AbstractPageSection 
        implements PageSectionInterface
        {

        /**
         * @var Page $obj
         */
        
        /////////////////////////////////////////////////
        /** 
         * Section HTML Template
         * This required HTML code will be used to render the final section content.
         * You can use tokens (see above) and you can run loops or any other PHP tricks you like.
         *
         * @returns string
         */
        public function section_html_template() {

            $node_html = '';
            $node_array = $this->get_node_array();

            foreach($node_array as $i => $node) {
                $node_html .= '
                
                <h2 class="h4 text-primary-800">{'.$i.':headline}</h2>
                <div class="col-12 col-md-9">
                {'.$i.':node_text}
                </div>
                {button_html}
                ';
            }

            $background_image = '';
            if ($this->section_row['attachment_filename']) {
                $background_image = ' style="background-image: url(/images/pages/'.htmlspecialchars($this->section_row['attachment_filename'],ENT_COMPAT).');"';
            }

            $section_headline = '';
            if ($this->section_row['section_title']) {
                $section_headline = '
                <h2 class="mb-5 text-center">' . htmlspecialchars($this->section_row['section_title'],ENT_COMPAT) . '</h2>
                ';
            }

            $section_html = '

            <section 
                id="'.htmlspecialchars($this->section_row['css_id'],ENT_COMPAT).'" 
                class="section section-2-col 
                    '.htmlspecialchars($this->section_type_row['css_class'],ENT_COMPAT).' 
                    '.htmlspecialchars($this->section_row['css_class'],ENT_COMPAT).'
                    '.htmlspecialchars($this->section_row['option_background_color'],ENT_COMPAT).'
                " 
                ' . $background_image . '
                >
                <div class="container-xl section-content">
                    ' . $section_headline . '
                    <div class="d-flex flex-row flex-wrap gap-5 justify-content-between align-items-stretch">
                        <div class="w-100 w-md-45">
                            <div class="mb-auto">
                                {0:node_text}
                            </div>
                            {0:button_html}
                        </div>
                        <div class="w-100 w-md-45">
                            {1:node_text}
                            <div class="text-center mb-4">
                                <i class="fa-solid fa-graduation-cap text-gray-200 mx-auto" style="font-size: 5rem;" aria-hidden="true"></i>
                            </div>
                            <div class="progress rounded-pill mt-4" style="height: 80px;">
                                <div class="progress-bar text-bg-primary" role="progressbar" style="width: 81%;" aria-valuenow="81" aria-valuemin="0" aria-valuemax="100">
                                    <div class="d-flex flex-row gap-3 justify-content-start align-items-center px-4 py-2 text-start">
                                        <div class="fs-56 fw-semibold">81%</div>
                                        <div class="fs-20 fw-normal lh-sm"><em>FIRST</em><br>Alumni</div>
                                    </div>
                                </div>
                            </div>
                            <div class="progress rounded-pill mt-4" style="height: 80px;">
                                <div class="progress-bar text-bg-gray-600" role="progressbar" style="width: 58%;" aria-valuenow="58" aria-valuemin="0" aria-valuemax="100">
                                    <div class="d-flex flex-row gap-3 justify-content-start align-items-center px-4 py-2 text-start">
                                        <div class="fs-56 fw-semibold">58%</div>
                                        <div class="fs-20 fw-normal lh-sm">Comparison<br>Group</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            ';
            return $section_html;
        }
            
        /////////////////////////////////////////////////
        /** 
         * Image HTML Template
         * This optional HTML code will be used to wrap any present images
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:image_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function image_html_template() { 
            $image_html = '
            <img src="/images/pages/{image}" alt="{alt_text}" class="img-fluid">
            ';
            return $image_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button HTML Template
         * This optional HTML code will be used to wrap any present buton text/URL
         * in any of the nodes for this section.  Note that in order for this to work,
         * you need to include the token {X:button_html} in the $section_html variable above.
         *
         * @returns string
         */
        public function button_html_template() {
            $button_html = '
            <a href="{url}" {external} class="btn btn-blue {css_class}">{text}</a>
            ';
            return $button_html;
        }
        
        /////////////////////////////////////////////////
        /** 
         * Button Wrapper HTML Template
         *
         * @returns string
         */
        public function button_wrapper_html_template() {
            $button_html = '
            <p class="mt-auto d-flex flex-row flex-wrap gap-3 justify-content-center justify-content-md-start aign-items-center">{button_html}</p>
            ';
            return $button_html;
        }
        
    } // end class
    
} // end if

