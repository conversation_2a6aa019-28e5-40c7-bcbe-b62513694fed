/**
 * Skill Progress Circle Animation Controller
 * Triggers animations when the section scrolls into view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create intersection observer to detect when section is in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start animations
                const progressCircles = entry.target.querySelectorAll('.skill-progress');
                progressCircles.forEach(circle => {
                    circle.classList.add('animate');
                });

                // Optional: Stop observing after animation starts (one-time trigger)
                observer.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 100px before the section enters viewport
        rootMargin: '0px 0px -100px 0px'
    });

    // Find and observe the impact skills section
    const skillsSection = document.querySelector('.section.impact_skills, [class*="impact_skills"]');
    if (skillsSection) {
        observer.observe(skillsSection);
    }

    // Fallback: Also look for any section containing skill-progress elements
    const sectionsWithSkillProgress = document.querySelectorAll('section:has(.skill-progress)');
    sectionsWithSkillProgress.forEach(section => {
        observer.observe(section);
    });

    // Additional fallback for older browsers that don't support :has()
    if (sectionsWithSkillProgress.length === 0) {
        const allSections = document.querySelectorAll('section');
        allSections.forEach(section => {
            if (section.querySelector('.skill-progress')) {
                observer.observe(section);
            }
        });
    }

    // Impact Education Progress Bar Animation
    // Create separate observer for impact education section
    const progressBarObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start progress bar animations
                entry.target.classList.add('animate-progress');

                // Optional: Stop observing after animation starts (one-time trigger)
                progressBarObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 40% of the section is visible
        threshold: 0.4,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Find and observe the impact education section
    const impactEducationSection = document.querySelector('.section.impact_education, [class*="impact_education"]');
    if (impactEducationSection) {
        progressBarObserver.observe(impactEducationSection);
    }

    // Fallback: Also look for any section containing the specific progress bars with graduation cap
    const sectionsWithProgressBars = document.querySelectorAll('section');
    sectionsWithProgressBars.forEach(section => {
        // Look for the specific pattern: graduation cap icon + progress bars
        const hasGraduationCap = section.querySelector('.fa-graduation-cap');
        const hasProgressBars = section.querySelectorAll('.progress-bar').length >= 2;

        if (hasGraduationCap && hasProgressBars) {
            progressBarObserver.observe(section);
        }
    });
});