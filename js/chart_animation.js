/**
 * Skill Progress Circle Animation Controller
 * Triggers animations when the section scrolls into view
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create intersection observer to detect when section is in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start animations
                const progressCircles = entry.target.querySelectorAll('.skill-progress');
                progressCircles.forEach(circle => {
                    circle.classList.add('animate');
                });

                // Optional: Stop observing after animation starts (one-time trigger)
                observer.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 100px before the section enters viewport
        rootMargin: '0px 0px -100px 0px'
    });

    // Find and observe the impact skills section
    const skillsSection = document.querySelector('.section.impact_skills, [class*="impact_skills"]');
    if (skillsSection) {
        observer.observe(skillsSection);
    }

    // Fallback: Also look for any section containing skill-progress elements
    const sectionsWithSkillProgress = document.querySelectorAll('section:has(.skill-progress)');
    sectionsWithSkillProgress.forEach(section => {
        observer.observe(section);
    });

    // Additional fallback for older browsers that don't support :has()
    if (sectionsWithSkillProgress.length === 0) {
        const allSections = document.querySelectorAll('section');
        allSections.forEach(section => {
            if (section.querySelector('.skill-progress')) {
                observer.observe(section);
            }
        });
    }

    // Impact Education Progress Bar Animation
    // Create separate observer for impact education section
    const progressBarObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Section is in view, start progress bar animations
                console.log('Impact education section in view, starting animation');
                entry.target.classList.add('animate-progress');

                // Optional: Stop observing after animation starts (one-time trigger)
                progressBarObserver.unobserve(entry.target);
            }
        });
    }, {
        // Trigger when 30% of the section is visible
        threshold: 0.3,
        // Start observing 50px before the section enters viewport
        rootMargin: '0px 0px -50px 0px'
    });

    // Multiple strategies to find the impact education section
    let foundSections = [];

    // Strategy 1: Look for sections with specific class patterns
    const classPatterns = [
        '.section.impact_education',
        '.section[class*="impact_education"]',
        '.section[class*="impact-education"]',
        '.impact_education',
        '.impact-education'
    ];

    classPatterns.forEach(pattern => {
        const sections = document.querySelectorAll(pattern);
        sections.forEach(section => {
            if (!foundSections.includes(section)) {
                foundSections.push(section);
                console.log('Found impact education section by class:', pattern);
            }
        });
    });

    // Strategy 2: Look for any section containing the specific pattern: graduation cap icon + multiple progress bars
    const allSections = document.querySelectorAll('section');
    allSections.forEach(section => {
        const hasGraduationCap = section.querySelector('.fa-graduation-cap');
        const progressBars = section.querySelectorAll('.progress-bar');
        const hasMultipleProgressBars = progressBars.length >= 2;

        if (hasGraduationCap && hasMultipleProgressBars && !foundSections.includes(section)) {
            foundSections.push(section);
            console.log('Found impact education section by content pattern');
        }
    });

    // Strategy 3: Look for sections containing specific text patterns (81% and 58%)
    allSections.forEach(section => {
        const text = section.textContent || section.innerText;
        const has81Percent = text.includes('81%');
        const has58Percent = text.includes('58%');
        const hasProgressBars = section.querySelectorAll('.progress-bar').length >= 2;

        if (has81Percent && has58Percent && hasProgressBars && !foundSections.includes(section)) {
            foundSections.push(section);
            console.log('Found impact education section by text pattern');
        }
    });

    // Observe all found sections
    if (foundSections.length > 0) {
        console.log(`Found ${foundSections.length} impact education section(s), starting observation`);
        foundSections.forEach(section => {
            progressBarObserver.observe(section);
        });
    } else {
        console.log('No impact education sections found');
    }
});